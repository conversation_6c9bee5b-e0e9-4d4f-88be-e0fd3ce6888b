﻿<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E站EhViewer - 你的次元漫游与创意空间</title>
    <script src="static/js/tailwindcss.js"></script>
    <link rel="shortcut icon" type="image/x-icon" href="static/picture/icon.png">
</head>
<body class="bg-[#EDFFFB]">
    <!-- 导航栏 -->
    <header class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <nav class="container mx-auto px-4 h-16 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <img src="static/picture/logo.png" alt="E站EhViewer" class="w-[159px] h-[36px]">
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="pt-16">
        <!-- Hero部分 -->
        <section class="container mx-auto px-4 py-8 md:py-24" style="background-image: url('static/picture/bg.png'); background-size: 100% 100%; background-position: center;">
            <div class="max-w-4xl mx-auto text-center">
                <div class="w-24 h-24 md:w-32 md:h-32 mx-auto mb-6">
                    <img src="static/picture/icon.png" alt="E站EhViewer">
                </div>
                <h1 class="text-4xl md:text-6xl font-bold text-[#06433C] mb-4">E站EhViewer</h1>
                <h2 class="text-2xl md:text-3xl font-bold text-[#06433C] mb-3">你的次元漫游与创意空间</h2>
                <p class="text-lg md:text-md text-[#06433C] opacity-60 mb-8">海量漫画随心读&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;智能清理更自由&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;创意编辑乐无穷</p>
                <a href="https://down.ehviewerapp.cn/channel/ehsl/EhViewer.apk" onclick="_hmt.push(['_trackEvent', '按钮', '点击', '下载按钮'])" class="inline-block">
                    <img src="static/picture/download.png" alt="下载" class="w-[218px]">
                </a>
            </div>
        </section>
      </main>
      <main class="pt-16 bg-white">
        <!-- 特性部分 -->
        <section class="container mx-auto px-4 py-8">
          <div class="grid grid-cols-3 gap-6 md:gap-8 max-w-4xl mx-auto">
              <!-- 特性1 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon1.png" alt="极简阅读" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">极简阅读</h3>
                  <p class="text-sm md:text-base text-[#06433f] opacity-60">流畅阅读体验</p>
              </div>

              <!-- 特性2 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon2.png" alt="精准分类" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">精准分类</h3>
                  <p class="text-sm md:text-base text-[#06433C] opacity-60">高效分类检索</p>
              </div>

              <!-- 特性3 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon3.png" alt="玩转个性表达" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">随心收藏</h3>
                  <p class="text-sm md:text-base text-[#06433C] opacity-60">云端收藏同步</p>
              </div>
          </div>
        </section>

        <section class="container mx-auto px-4 py-8 bg-white">
          <div class="grid grid-cols-4 gap-6 md:gap-8 max-w-4xl mx-auto">
              <!-- 特性1 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon4.png" alt="释放设备空间" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">释放设备空间</h3>
                  <p class="text-sm md:text-base text-[#06433C] opacity-60">一键深度清理</p>
              </div>

              <!-- 特性2 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon5.png" alt="创意图片工坊" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">创意图片工坊</h3>
                  <p class="text-sm md:text-base text-[#06433C] opacity-60">基础编辑利器</p>
              </div>

              <!-- 特性3 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon6.png" alt="创意图片工坊" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">玩转个性表达</h3>
                  <p class="text-sm md:text-base text-[#06433C] opacity-60">趣味创作套件</p>
              </div>

              <!-- 特性4 -->
              <div class="text-center">
                  <div class="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 flex items-center justify-center">
                      <img src="static/picture/icon7.png" alt="智能文件清理" class="w-16 h-16 md:w-20 md:h-20">
                  </div>
                  <h3 class="text-base md:text-lg font-bold mb-1">智能文件清理</h3>
                  <p class="text-sm md:text-base text-[#06433C] opacity-60">精准扫描冗余</p>
              </div>
          </div>
        </section>
      </main>
      <main class="bg-[#EDFFFB]">
        <!-- 为什么选择我们 -->
        <section class="py-12 md:py-20">
            <div class="container mx-auto px-4">
                <h2 class="text-2xl md:text-4xl font-bold text-[#06433C] mb-8 text-center">为什么选择E站EhViewer</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
                    <div class="bg-white rounded-2xl p-6 md:p-8">
                        <h3 class="text-xl md:text-2xl font-bold text-[#06433C] mb-3">免登录轻量化</h3>
                        <p class="text-base md:text-lg text-[#06433C] opacity-60">无需注册/登录，安装即用，所有阅读记录与收藏自动本地存储，轻触图标秒速进入漫画世界。</p>
                    </div>
                    <div class="bg-white rounded-2xl p-6 md:p-8">
                        <h3 class="text-xl md:text-2xl font-bold text-[#06433C] mb-3">用户需求驱动进化</h3>
                        <p class="text-base md:text-lg text-[#06433C] opacity-60">每月功能更新+季度界面焕新，你的吐槽建议直达开发组
                          立即下载，开启多维次元之旅！</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-white py-6 md:py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="text-sm md:text-base text-[#072F32] opacity-80">联系我们：<EMAIL>    备案号：<a href="https://beian.miit.gov.cn/" target="_blank">京ICP证000000号</a></p>
        </div>
    </footer>

</body>
</html> 
